# Vite Environment Variables for Network Access

# API Base URL - Leave empty to auto-detect based on current host
# For localhost: http://localhost:3000/api
# For network access: http://[YOUR_IP]:3000/api
VITE_API_URL=

# Socket.IO URL - Leave empty to auto-detect based on current host  
# For localhost: http://localhost:3000
# For network access: http://[YOUR_IP]:3000
VITE_SOCKET_URL=

# Development Notes:
# - When running on localhost, these can be left empty
# - When accessing from other devices on LAN, these will auto-detect the correct IP
# - For production, set these to your actual server URLs
