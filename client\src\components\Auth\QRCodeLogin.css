/* QR Code Login Styles */
.qr-login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--tg-spacing-xxl);
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.qr-code-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--tg-spacing-lg);
}

.qr-code-wrapper {
    position: relative;
    width: 240px;
    height: 240px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--tg-radius-large);
    padding: var(--tg-spacing-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(64, 167, 227, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all var(--tg-transition-normal);
}

.qr-code-wrapper.waiting {
    animation: qrPulse 2s ease-in-out infinite;
}

.qr-code-wrapper.expired {
    opacity: 0.6;
    filter: grayscale(50%);
}

.qr-code-wrapper.success {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
}

.qr-code-display {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.qr-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(64, 167, 227, 0.3);
    border-top: 3px solid #40a7e3;
    border-radius: 50%;
    animation: tg-spin 1s linear infinite;
}

.qr-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--tg-spacing-md);
    width: 100%;
    height: 100%;
}

.success-icon {
    font-size: 48px;
    animation: successBounce 0.6s ease-out;
}

.success-text {
    font-size: var(--tg-font-size-medium);
    font-weight: var(--tg-font-weight-medium);
    color: white;
}

.qr-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    border-radius: var(--tg-radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.expired-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--tg-spacing-md);
    color: white;
    text-align: center;
}

.expired-icon {
    font-size: 32px;
    opacity: 0.8;
}

.expired-text {
    font-size: var(--tg-font-size-normal);
    font-weight: var(--tg-font-weight-medium);
}

.refresh-btn {
    background: var(--tg-button-color);
    color: white;
    border: none;
    padding: var(--tg-spacing-sm) var(--tg-spacing-lg);
    border-radius: var(--tg-radius-medium);
    font-size: var(--tg-font-size-small);
    font-weight: var(--tg-font-weight-medium);
    cursor: pointer;
    transition: all var(--tg-transition-normal);
}

.refresh-btn:hover {
    background: var(--tg-primary-dark);
    transform: translateY(-1px);
}

.qr-status {
    display: flex;
    align-items: center;
    gap: var(--tg-spacing-md);
    padding: var(--tg-spacing-md) var(--tg-spacing-lg);
    background: rgba(45, 45, 45, 0.9);
    border: 1px solid rgba(64, 167, 227, 0.3);
    border-radius: var(--tg-radius-medium);
    backdrop-filter: blur(10px);
}

.status-icon {
    font-size: 20px;
}

.status-message {
    font-size: var(--tg-font-size-normal);
    color: #ffffff;
    font-weight: var(--tg-font-weight-normal);
}

.qr-instructions {
    background: rgba(45, 45, 45, 0.9);
    border: 1px solid rgba(64, 167, 227, 0.3);
    border-radius: var(--tg-radius-large);
    padding: var(--tg-spacing-xl);
    backdrop-filter: blur(10px);
    width: 100%;
    box-sizing: border-box;
}

.qr-instructions h3 {
    margin: 0 0 var(--tg-spacing-lg) 0;
    font-size: var(--tg-font-size-medium);
    font-weight: var(--tg-font-weight-medium);
    color: #ffffff;
}

.qr-instructions ol {
    margin: 0;
    padding-left: var(--tg-spacing-xl);
    color: rgba(255, 255, 255, 0.9);
}

.qr-instructions li {
    margin-bottom: var(--tg-spacing-sm);
    font-size: var(--tg-font-size-normal);
    line-height: 1.5;
}

.qr-alternative {
    width: 100%;
    text-align: center;
}

.switch-to-form-btn {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(64, 167, 227, 0.3);
    padding: var(--tg-spacing-md) var(--tg-spacing-xl);
    border-radius: var(--tg-radius-medium);
    font-size: var(--tg-font-size-normal);
    font-weight: var(--tg-font-weight-normal);
    cursor: pointer;
    transition: all var(--tg-transition-normal);
    font-family: var(--tg-font-family);
}

.switch-to-form-btn:hover {
    background: rgba(64, 167, 227, 0.1);
    border-color: rgba(64, 167, 227, 0.5);
    color: #ffffff;
}

/* Animations */
@keyframes qrPulse {
    0%, 100% {
        box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(64, 167, 227, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }
    50% {
        box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(64, 167, 227, 0.4),
            0 0 20px rgba(64, 167, 227, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }
}

@keyframes successBounce {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .qr-code-wrapper {
        width: 200px;
        height: 200px;
        padding: var(--tg-spacing-lg);
    }
    
    .qr-instructions {
        padding: var(--tg-spacing-lg);
    }
    
    .qr-instructions h3 {
        font-size: var(--tg-font-size-normal);
    }
    
    .qr-instructions li {
        font-size: var(--tg-font-size-small);
    }
}

@media (max-width: 480px) {
    .qr-code-wrapper {
        width: 180px;
        height: 180px;
        padding: var(--tg-spacing-md);
    }
    
    .qr-login-container {
        gap: var(--tg-spacing-lg);
    }
    
    .success-icon {
        font-size: 36px;
    }
    
    .expired-icon {
        font-size: 24px;
    }
}
