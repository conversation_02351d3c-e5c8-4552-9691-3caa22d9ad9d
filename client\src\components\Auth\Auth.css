/* Telegram Authentication Styles */
/* Using the global Telegram theme system from telegram-theme.css */

/* Telegram Authentication Container */
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 50%, #1a1a1a 100%);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    font-family: var(--tg-font-family);
}

/* Enhanced background with subtle animated patterns */
.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(64, 167, 227, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(100, 181, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(64, 167, 227, 0.05) 0%, transparent 50%);
    animation: tg-float 20s ease-in-out infinite;
    pointer-events: none;
}

/* Removed grid pattern - replaced with animated chat background */

@keyframes tg-float {

    0%,
    100% {
        transform: translateY(0px) scale(1);
        opacity: 1;
    }

    33% {
        transform: translateY(-20px) scale(1.02);
        opacity: 0.8;
    }

    66% {
        transform: translateY(-10px) scale(0.98);
        opacity: 0.9;
    }
}

/* Telegram Header Bar */
.auth-header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background: rgba(30, 30, 30, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(64, 167, 227, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--tg-spacing-xl);
    z-index: 1000;
    transition: all var(--tg-transition-slow);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.auth-header-spacer {
    width: 40px;
    /* Same width as theme toggle would be */
    height: 40px;
}

.auth-logo {
    display: flex;
    align-items: center;
    gap: var(--tg-spacing-md);
    font-weight: var(--tg-font-weight-medium);
    font-size: var(--tg-font-size-medium);
    color: var(--tg-text-color);
    transition: all var(--tg-transition-normal);
}

.auth-logo-icon {
    font-size: 24px;
    filter: drop-shadow(0 2px 4px rgba(64, 167, 227, 0.3));
    transition: transform var(--tg-transition-normal);
}

.auth-logo:hover .auth-logo-icon {
    transform: scale(1.1);
}

/* Main Content Area */
.auth-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px var(--tg-spacing-xl) 40px;
    max-width: 400px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

/* Telegram Authentication Card */
.auth-card {
    background: rgba(45, 45, 45, 0.95);
    border-radius: var(--tg-radius-large);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(64, 167, 227, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: var(--tg-spacing-xxl);
    width: 100%;
    max-width: 400px;
    position: relative;
    z-index: 10;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(64, 167, 227, 0.2);
    animation: tg-slideUp var(--tg-transition-slow) ease-out;
    overflow: hidden;
}

/* Glass morphism effect for auth card */
.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            rgba(64, 167, 227, 0.05) 100%);
    border-radius: var(--tg-radius-large);
    pointer-events: none;
    z-index: -1;
}

/* Telegram Page Header */
.auth-page-header {
    text-align: center;
    margin-bottom: var(--tg-spacing-xxl);
    padding: 0 var(--tg-spacing-xl);
}

/* Login Method Toggle */
.login-method-toggle {
    display: flex;
    background: rgba(30, 30, 30, 0.8);
    border-radius: var(--tg-radius-medium);
    padding: 4px;
    margin-bottom: var(--tg-spacing-xl);
    border: 1px solid rgba(64, 167, 227, 0.2);
}

.method-btn {
    flex: 1;
    padding: var(--tg-spacing-md) var(--tg-spacing-lg);
    background: transparent;
    border: none;
    border-radius: calc(var(--tg-radius-medium) - 4px);
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--tg-font-size-normal);
    font-weight: var(--tg-font-weight-medium);
    cursor: pointer;
    transition: all var(--tg-transition-normal);
    font-family: var(--tg-font-family);
}

.method-btn:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(64, 167, 227, 0.1);
}

.method-btn.active {
    background: linear-gradient(135deg, #64b5f6 0%, #40a7e3 50%, #2196f3 100%);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(64, 167, 227, 0.3);
}

.auth-page-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--tg-spacing-xxl);
    background: linear-gradient(135deg, #64b5f6 0%, #40a7e3 50%, #2196f3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    color: var(--tg-button-text);
    box-shadow:
        0 8px 32px rgba(64, 167, 227, 0.4),
        0 0 0 4px rgba(64, 167, 227, 0.1),
        inset 0 2px 0 rgba(255, 255, 255, 0.2);
    transition: all var(--tg-transition-normal);
    position: relative;
    overflow: hidden;
}

.auth-page-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: tg-shine 3s ease-in-out infinite;
}

.auth-page-icon:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow:
        0 12px 40px rgba(64, 167, 227, 0.5),
        0 0 0 4px rgba(64, 167, 227, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

@keyframes tg-shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    50% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }

    100% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
}

.auth-page-title {
    font-size: var(--tg-font-size-xlarge);
    font-weight: var(--tg-font-weight-normal);
    color: var(--tg-text-color);
    margin: 0 0 var(--tg-spacing-md) 0;
    letter-spacing: -0.5px;
}

.auth-page-subtitle {
    font-size: var(--tg-font-size-normal);
    color: var(--tg-hint-color);
    margin: 0;
    line-height: 1.4;
    font-weight: var(--tg-font-weight-normal);
}

/* Telegram Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 0;
    width: 100%;
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.form-group {
    position: relative;
    margin-bottom: 1px;
}

.form-group label {
    display: none;
    /* Telegram doesn't show labels, uses placeholders */
}

/* Telegram Input Styles */
.form-group input {
    width: 100%;
    padding: var(--tg-spacing-lg) var(--tg-spacing-xl);
    border: none;
    border-bottom: 1px solid rgba(64, 167, 227, 0.2);
    background: rgba(45, 45, 45, 0.8);
    font-size: var(--tg-font-size-medium);
    color: #ffffff;
    transition: all var(--tg-transition-normal);
    border-radius: 0;
    box-sizing: border-box;
    font-family: var(--tg-font-family);
    font-weight: var(--tg-font-weight-normal);
    position: relative;
}

.form-group:first-child input {
    border-top: 1px solid rgba(64, 167, 227, 0.2);
    border-radius: var(--tg-radius-medium) var(--tg-radius-medium) 0 0;
}

.form-group:last-child input {
    border-radius: 0 0 var(--tg-radius-medium) var(--tg-radius-medium);
}

.form-group:only-child input {
    border-radius: var(--tg-radius-medium);
    border: 1px solid rgba(64, 167, 227, 0.2);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
    font-weight: var(--tg-font-weight-normal);
}

.form-group input:focus {
    outline: none;
    border-bottom-color: #64b5f6;
    background: rgba(55, 55, 55, 0.9);
    box-shadow:
        0 0 0 3px rgba(64, 167, 227, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.form-group input:hover:not(:focus) {
    background: rgba(50, 50, 50, 0.9);
    border-bottom-color: rgba(64, 167, 227, 0.3);
}

.form-group input:disabled {
    background: rgba(40, 40, 40, 0.6);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Enhanced input focus effects */
.form-group {
    position: relative;
}

.form-group::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #64b5f6, #40a7e3);
    transition: all var(--tg-transition-normal);
    transform: translateX(-50%);
}

.form-group input:focus+.form-group::after,
.form-group:focus-within::after {
    width: 100%;
}

/* Telegram Button Styles */
.auth-button {
    background: linear-gradient(135deg, #64b5f6 0%, #40a7e3 50%, #2196f3 100%);
    color: var(--tg-button-text);
    border: none;
    padding: var(--tg-spacing-lg) var(--tg-spacing-xxl);
    border-radius: var(--tg-radius-medium);
    font-size: var(--tg-font-size-medium);
    font-weight: var(--tg-font-weight-medium);
    cursor: pointer;
    transition: all var(--tg-transition-normal);
    margin-top: var(--tg-spacing-xxl);
    width: 100%;
    box-sizing: border-box;
    font-family: var(--tg-font-family);
    letter-spacing: 0.3px;
    box-shadow:
        0 4px 12px rgba(64, 167, 227, 0.3),
        0 0 0 1px rgba(64, 167, 227, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    min-height: 48px;
}

.auth-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--tg-transition-slow);
}

.auth-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #5aa3f0 0%, #3498db 50%, #1976d2 100%);
    transform: translateY(-2px);
    box-shadow:
        0 8px 20px rgba(64, 167, 227, 0.4),
        0 0 0 1px rgba(64, 167, 227, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.auth-button:hover:not(:disabled)::before {
    left: 100%;
}

.auth-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow:
        0 2px 8px rgba(64, 167, 227, 0.3),
        0 0 0 1px rgba(64, 167, 227, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.auth-button:disabled {
    background: var(--tg-hint-color);
    cursor: not-allowed;
    transform: none;
    opacity: 0.6;
    box-shadow: none;
}

.auth-button:disabled::before {
    display: none;
}

/* Telegram Error Message */
.error-message {
    background: rgba(229, 62, 62, 0.15);
    color: #ff6b6b;
    padding: var(--tg-spacing-lg) var(--tg-spacing-xl);
    border-radius: var(--tg-radius-medium);
    border: 1px solid rgba(229, 62, 62, 0.3);
    font-size: var(--tg-font-size-normal);
    text-align: center;
    font-weight: var(--tg-font-weight-normal);
    margin-bottom: var(--tg-spacing-xl);
    line-height: 1.4;
    animation: tg-fadeIn var(--tg-transition-normal) ease-in, errorShake 0.5s ease-in-out;
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.2);
    backdrop-filter: blur(10px);
}

/* Telegram Success Message */
.success-message {
    background: rgba(75, 181, 67, 0.15);
    color: #4bb543;
    padding: var(--tg-spacing-lg) var(--tg-spacing-xl);
    border-radius: var(--tg-radius-medium);
    border: 1px solid rgba(75, 181, 67, 0.3);
    font-size: var(--tg-font-size-normal);
    text-align: center;
    font-weight: var(--tg-font-weight-normal);
    margin-bottom: var(--tg-spacing-xl);
    line-height: 1.4;
    animation: tg-fadeIn var(--tg-transition-normal) ease-in;
    box-shadow: 0 4px 12px rgba(75, 181, 67, 0.2);
    backdrop-filter: blur(10px);
}

/* Loading state for auth button */
.auth-button.loading {
    pointer-events: none;
    position: relative;
}

.auth-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: tg-spin 1s linear infinite;
}

.auth-button.loading span {
    opacity: 0;
}

/* Telegram Footer */
.auth-footer {
    text-align: center;
    margin-top: var(--tg-spacing-xxl);
    padding: var(--tg-spacing-xl);
}

.auth-footer p {
    color: var(--tg-hint-color);
    font-size: var(--tg-font-size-normal);
    margin: 0;
    font-weight: var(--tg-font-weight-normal);
    line-height: 1.4;
}

.auth-link {
    color: var(--tg-link-color);
    text-decoration: none;
    font-weight: var(--tg-font-weight-medium);
    transition: opacity var(--tg-transition-normal);
}

.auth-link:hover {
    opacity: 0.8;
}

/* Telegram Theme Toggle */
.theme-toggle {
    background: transparent;
    border: none;
    color: var(--tg-text-color);
    cursor: pointer;
    font-size: 20px;
    padding: var(--tg-spacing-sm);
    border-radius: 50%;
    transition: all var(--tg-transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.theme-toggle:hover {
    background: var(--tg-secondary-bg);
    transform: scale(1.1);
}

/* Telegram Responsive Design */
@media (max-width: 768px) {
    .auth-content {
        padding: 60px var(--tg-spacing-lg) var(--tg-spacing-xl);
    }

    .auth-header-bar {
        padding: 0 var(--tg-spacing-lg);
    }

    .auth-page-icon {
        width: 100px;
        height: 100px;
        font-size: 50px;
    }

    .auth-page-title {
        font-size: 28px;
    }

    .auth-page-subtitle {
        font-size: var(--tg-font-size-normal);
    }

    .auth-card {
        padding: var(--tg-spacing-xl);
    }
}

@media (max-width: 480px) {
    .auth-content {
        padding: 60px var(--tg-spacing-md) var(--tg-spacing-lg);
    }

    .auth-header-bar {
        padding: 0 var(--tg-spacing-md);
        height: 48px;
    }

    .auth-logo {
        font-size: var(--tg-font-size-normal);
    }

    .auth-logo-icon {
        font-size: 20px;
    }

    .auth-page-header {
        margin-bottom: var(--tg-spacing-xl);
        padding: 0 var(--tg-spacing-md);
    }

    .auth-page-icon {
        width: 80px;
        height: 80px;
        font-size: 40px;
        margin-bottom: var(--tg-spacing-xl);
    }

    .auth-page-title {
        font-size: 24px;
    }

    .auth-page-subtitle {
        font-size: var(--tg-font-size-small);
    }

    .form-group input {
        padding: var(--tg-spacing-lg) var(--tg-spacing-lg);
        font-size: 16px;
        /* Prevent zoom on iOS */
    }

    .auth-button {
        padding: var(--tg-spacing-lg) var(--tg-spacing-xl);
        font-size: 16px;
        /* Prevent zoom on iOS */
        min-height: 44px;
        /* Touch-friendly size */
    }

    .theme-toggle {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }

    .auth-card {
        padding: var(--tg-spacing-lg);
        margin: var(--tg-spacing-md);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .auth-page-icon {
        border: 2px solid var(--tg-text-color);
    }

    .form-group input {
        border: 2px solid var(--tg-border-color);
    }

    .auth-button {
        border: 2px solid var(--tg-button-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

    .auth-page-icon,
    .auth-button,
    .theme-toggle {
        transition: none;
    }

    .auth-card {
        animation: none;
    }
}

/* Error shake animation for better UX feedback */
@keyframes errorShake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}