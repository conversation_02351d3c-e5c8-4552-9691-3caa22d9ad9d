const express = require('express');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const db = require('../database/db');
const { generateToken } = require('../middleware/auth');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Register new user
router.post('/register', async (req, res) => {
    try {
        const { username, email, phone, password, first_name, last_name } = req.body;

        // Validate required fields
        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password are required' });
        }

        // Check if user already exists
        const existingUser = await db.getUserByUsername(username);
        if (existingUser) {
            return res.status(400).json({ error: 'Username already exists' });
        }

        // Hash password
        const saltRounds = 10;
        const password_hash = await bcrypt.hash(password, saltRounds);

        // Create user
        const userData = {
            username,
            email: email || null,
            phone: phone || null,
            password_hash,
            first_name: first_name || '',
            last_name: last_name || ''
        };

        const user = await db.createUser(userData);

        // Generate token
        const token = generateToken(user);

        // Create session
        const sessionData = {
            user_id: user.id,
            session_token: token,
            device_info: JSON.stringify(req.headers['user-agent'] || 'Unknown'),
            ip_address: req.ip || req.connection.remoteAddress
        };

        await db.createSession(sessionData);

        // Update user status to online
        await db.updateUserStatus(user.id, 'online', true);

        res.status(201).json({
            message: 'User registered successfully',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                first_name: user.first_name,
                last_name: user.last_name
            },
            token
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Login user
router.post('/login', async (req, res) => {
    try {
        const { username, password, sessionId, qrAuth } = req.body;

        // Handle QR code authentication
        if (qrAuth && sessionId) {
            const { qrSessions } = require('./sessions');
            const qrSession = qrSessions.get(sessionId);

            if (!qrSession || qrSession.status !== 'authenticated') {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid or expired QR session'
                });
            }

            // QR session should have the token already
            return res.json({
                success: true,
                message: 'QR Login successful',
                token: qrSession.token
            });
        }

        // Regular username/password authentication
        if (!username || !password) {
            return res.status(400).json({ error: 'Username and password are required' });
        }

        // Find user
        const user = await db.getUserByUsername(username);
        if (!user) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Generate token
        const token = generateToken(user);

        // Create session
        const sessionData = {
            user_id: user.id,
            session_token: token,
            device_info: JSON.stringify(req.headers['user-agent'] || 'Unknown'),
            ip_address: req.ip || req.connection.remoteAddress
        };

        await db.createSession(sessionData);

        // Update user status to online
        await db.updateUserStatus(user.id, 'online', true);

        res.json({
            message: 'Login successful',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                first_name: user.first_name,
                last_name: user.last_name,
                avatar_url: user.avatar_url,
                bio: user.bio
            },
            token
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Logout user
router.post('/logout', authenticateToken, async (req, res) => {
    try {
        // Delete session
        await db.deleteSession(req.token);

        // Update user status to offline
        await db.updateUserStatus(req.user.id, 'offline', false);

        res.json({ message: 'Logout successful' });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
    try {
        const user = await db.getUserById(req.user.id);
        
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json({
            id: user.id,
            username: user.username,
            email: user.email,
            phone: user.phone,
            first_name: user.first_name,
            last_name: user.last_name,
            bio: user.bio,
            avatar_url: user.avatar_url,
            status: user.status,
            is_online: user.is_online,
            last_seen: user.last_seen,
            created_at: user.created_at
        });

    } catch (error) {
        console.error('Profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Verify token (for frontend to check if user is still authenticated)
router.get('/verify', authenticateToken, (req, res) => {
    res.json({ 
        valid: true, 
        user: req.user 
    });
});

module.exports = router;
