import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../services/api';
import socketService from '../services/socket';

const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [token, setToken] = useState(localStorage.getItem('token'));
    const [loading, setLoading] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState(false);

    useEffect(() => {
        const initAuth = async () => {
            const storedToken = localStorage.getItem('token');
            const storedUser = localStorage.getItem('user');

            if (storedToken && storedUser) {
                try {
                    // Verify token is still valid
                    const response = await authAPI.verifyToken();
                    if (response.data.valid) {
                        setToken(storedToken);
                        setUser(JSON.parse(storedUser));
                        setIsAuthenticated(true);

                        // Connect to socket
                        socketService.connect(storedToken);
                    } else {
                        // Token is invalid, clear storage
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                } catch (error) {
                    console.error('Token verification failed:', error);
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
            }
            setLoading(false);
        };

        initAuth();
    }, []);

    const login = async (credentials) => {
        try {
            const response = await authAPI.login(credentials);
            const { user: userData, token: userToken } = response.data;

            setUser(userData);
            setToken(userToken);
            setIsAuthenticated(true);

            localStorage.setItem('token', userToken);
            localStorage.setItem('user', JSON.stringify(userData));

            // Connect to socket
            socketService.connect(userToken);

            return { success: true, user: userData };
        } catch (error) {
            console.error('Login error:', error);

            // Handle specific error cases
            let errorMessage = 'Login failed';

            if (error.response?.data?.error) {
                const serverError = error.response.data.error;

                // Handle invalid credentials
                if (serverError.includes('Invalid credentials') || serverError.includes('password')) {
                    errorMessage = 'Invalid username or password. Please check your credentials and try again.';
                }
                // Handle user not found
                else if (serverError.includes('User not found') || serverError.includes('not found')) {
                    errorMessage = 'Account not found. Please check your username or email address.';
                }
                // Handle account locked/disabled
                else if (serverError.includes('disabled') || serverError.includes('locked')) {
                    errorMessage = 'Your account has been disabled. Please contact support.';
                }
                // Use server error message for other cases
                else {
                    errorMessage = serverError;
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            return {
                success: false,
                error: errorMessage
            };
        }
    };

    const register = async (userData) => {
        try {
            const response = await authAPI.register(userData);
            // Don't automatically log in - just return success
            return {
                success: true,
                message: 'Account created successfully! Please log in with your credentials.'
            };
        } catch (error) {
            console.error('Registration error:', error);

            // Handle specific error cases
            let errorMessage = 'Registration failed';

            if (error.response?.data?.error) {
                const serverError = error.response.data.error;

                // Handle duplicate email error
                if (serverError.includes('email') && serverError.includes('already')) {
                    errorMessage = 'This email address is already registered. Please use a different email or try logging in.';
                }
                // Handle duplicate username error
                else if (serverError.includes('username') && serverError.includes('already')) {
                    errorMessage = 'This username is already taken. Please choose a different username.';
                }
                // Handle duplicate phone error
                else if (serverError.includes('phone') && serverError.includes('already')) {
                    errorMessage = 'This phone number is already registered. Please use a different phone number.';
                }
                // Handle validation errors
                else if (serverError.includes('validation') || serverError.includes('invalid')) {
                    errorMessage = 'Please check your information and try again.';
                }
                // Use server error message for other cases
                else {
                    errorMessage = serverError;
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            return {
                success: false,
                error: errorMessage
            };
        }
    };

    const logout = async () => {
        try {
            await authAPI.logout();
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            setUser(null);
            setToken(null);
            setIsAuthenticated(false);

            localStorage.removeItem('token');
            localStorage.removeItem('user');

            // Disconnect socket
            socketService.disconnect();
        }
    };

    const value = {
        user,
        token,
        isAuthenticated,
        loading,
        login,
        register,
        logout,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};
