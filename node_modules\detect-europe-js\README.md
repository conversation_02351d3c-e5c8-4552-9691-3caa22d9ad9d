# detectEurope.js

Detect if a user is from a country around the European Union (EU), EFTA, EEA, or Schengen area based on their timezone settings.

```sh
npm i detect-europe-js
```

### * `isFromEU(): boolean`

European Union (EU) countries

### * `isFromEEA(): boolean`

EU countries + Iceland, Liechtenstein and Norway

### * `isFromEFTA(): boolean`

Switzerland + Iceland, Liechtenstein and Norway

### * `isFromSchengen(): boolean`

Schengen countries (including Romania, Bulgaria, and several microstates)

## Code Example

```js
import { isFromEU } from 'detect-europe-js';

if(isFromEU()) {
    // show cookie banner
}
```