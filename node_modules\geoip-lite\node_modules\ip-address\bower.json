{"name": "ip-address", "description": "A library for parsing IPv6 and IPv4 IP addresses in node and the browser.", "main": "dist/ip-address-globals.js", "authors": ["<PERSON> <<EMAIL>> (https://beaugunderson.com/)"], "license": "MIT", "keywords": ["ipv6", "ipv4", "browser", "validation"], "homepage": "https://github.com/beaugunderson/ip-address", "moduleType": ["globals"], "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}