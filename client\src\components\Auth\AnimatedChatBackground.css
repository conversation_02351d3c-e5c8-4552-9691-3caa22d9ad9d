/* Animated Chat Background */
.animated-chat-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.chat-bubbles-container {
    position: relative;
    width: 100%;
    height: 100%;
}

/* Chat Bubble Styles */
.chat-bubble {
    position: absolute;
    max-width: 280px;
    min-width: 120px;
    padding: 12px 16px;
    border-radius: 18px;
    backdrop-filter: blur(10px);
    animation: bubbleFloat 12s ease-in-out infinite;
    opacity: 0;
    animation-fill-mode: forwards;
    left: var(--random-x);
    top: var(--random-y);
    transform: translateX(-50%) translateY(-50%);
}

.chat-bubble.incoming {
    background: rgba(45, 45, 45, 0.8);
    border: 1px solid rgba(64, 167, 227, 0.3);
    color: #ffffff;
    animation-name: bubbleFloatIncoming;
}

.chat-bubble.outgoing {
    background: rgba(64, 167, 227, 0.8);
    border: 1px solid rgba(100, 181, 246, 0.3);
    color: #ffffff;
    animation-name: bubbleFloatOutgoing;
}

.bubble-content {
    position: relative;
    z-index: 2;
}

.bubble-sender {
    font-size: 11px;
    font-weight: 500;
    opacity: 0.8;
    margin-bottom: 4px;
    color: rgba(255, 255, 255, 0.9);
}

.bubble-text {
    font-size: 14px;
    line-height: 1.4;
    font-weight: 400;
}

.bubble-tail {
    position: absolute;
    bottom: 6px;
    width: 0;
    height: 0;
    z-index: 1;
}

.chat-bubble.incoming .bubble-tail {
    left: -6px;
    border-right: 8px solid rgba(45, 45, 45, 0.8);
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
}

.chat-bubble.outgoing .bubble-tail {
    right: -6px;
    border-left: 8px solid rgba(64, 167, 227, 0.8);
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
}

/* Bubble Animations */
@keyframes bubbleFloatIncoming {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(-50%) scale(0.95) translateX(-20px);
    }

    15% {
        opacity: 0.6;
        transform: translateX(-50%) translateY(-50%) scale(1) translateX(0);
    }

    85% {
        opacity: 0.6;
        transform: translateX(-50%) translateY(-50%) scale(1) translateX(0);
    }

    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-50%) scale(0.95) translateX(20px);
    }
}

@keyframes bubbleFloatOutgoing {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(-50%) scale(0.95) translateX(20px);
    }

    15% {
        opacity: 0.6;
        transform: translateX(-50%) translateY(-50%) scale(1) translateX(0);
    }

    85% {
        opacity: 0.6;
        transform: translateX(-50%) translateY(-50%) scale(1) translateX(0);
    }

    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-50%) scale(0.95) translateX(-20px);
    }
}

/* Feature Highlights */
.feature-highlights {
    position: absolute;
    top: 10%;
    right: 5%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: rgba(30, 30, 30, 0.9);
    border: 1px solid rgba(64, 167, 227, 0.3);
    border-radius: 25px;
    backdrop-filter: blur(15px);
    animation: featureSlideIn 1s ease-out var(--delay) both;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    font-size: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(64, 167, 227, 0.2);
    border-radius: 50%;
}

.feature-text {
    font-size: 13px;
    font-weight: 500;
    color: #ffffff;
    white-space: nowrap;
}

@keyframes featureSlideIn {
    0% {
        opacity: 0;
        transform: translateX(100px);
    }

    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Typing Indicators */
.typing-indicators {
    position: absolute;
    bottom: 15%;
    left: 10%;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: rgba(45, 45, 45, 0.9);
    border: 1px solid rgba(64, 167, 227, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    animation: typingSlideIn 1s ease-out var(--delay) both;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: rgba(64, 167, 227, 0.8);
    border-radius: 50%;
    animation: typingDot 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

.typing-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
}

@keyframes typingDot {

    0%,
    60%,
    100% {
        transform: scale(1);
        opacity: 0.5;
    }

    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes typingSlideIn {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-bubble {
        max-width: 200px;
        padding: 10px 14px;
        font-size: 13px;
    }

    .feature-highlights {
        right: 2%;
        top: 15%;
    }

    .feature-highlight {
        padding: 8px 12px;
        gap: 8px;
    }

    .feature-icon {
        font-size: 16px;
        width: 24px;
        height: 24px;
    }

    .feature-text {
        font-size: 11px;
    }

    .typing-indicator {
        padding: 8px 12px;
    }
}

@media (max-width: 480px) {
    .chat-bubble {
        max-width: 160px;
        padding: 8px 12px;
    }

    .feature-highlights {
        display: none;
        /* Hide on very small screens */
    }

    .typing-indicators {
        bottom: 10%;
        left: 5%;
    }
}