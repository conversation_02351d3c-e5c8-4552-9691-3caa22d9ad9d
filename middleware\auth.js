const jwt = require('jsonwebtoken');
const db = require('../database/db');

const JWT_SECRET = process.env.JWT_SECRET || 'a8f5f167f44f4964e6c998dee827110c8b6d8b7c9b9d1f2e4a5b6c7d8e9f0a1b2c3d4e5f6';

const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, JWT_SECRET);
        
        // Check if session exists in database
        const session = await db.getSessionByToken(token);
        
        if (!session) {
            return res.status(401).json({ error: 'Invalid or expired session' });
        }

        // Add user info to request
        req.user = {
            id: session.user_id,
            username: session.username,
            email: session.email,
            first_name: session.first_name,
            last_name: session.last_name
        };

        req.token = token;
        req.sessionId = decoded.sessionId; // Extract session ID from JWT
        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        return res.status(403).json({ error: 'Invalid token' });
    }
};

const generateToken = (user) => {
    return jwt.sign(
        { 
            id: user.id, 
            username: user.username 
        },
        JWT_SECRET,
        { expiresIn: '7d' }
    );
};

module.exports = {
    authenticateToken,
    generateToken,
    JWT_SECRET
};
