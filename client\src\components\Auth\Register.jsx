import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import AnimatedChat<PERSON>ackground from './AnimatedChatBackground';
import './Auth.css';

const Register = () => {
    const [formData, setFormData] = useState({
        username: '',
        email: '',
        phone: '',
        password: '',
        confirmPassword: '',
        first_name: '',
        last_name: ''
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const { register } = useAuth();
    const { setAuthMode } = useTheme();
    const navigate = useNavigate();

    // Set auth mode when component mounts
    useEffect(() => {
        setAuthMode(true);
        return () => setAuthMode(false); // Cleanup when component unmounts
    }, [setAuthMode]);

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
        // Clear error when user starts typing
        if (error) setError('');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validation
        if (!formData.username || !formData.password) {
            setError('Username and password are required');
            return;
        }

        if (formData.password !== formData.confirmPassword) {
            setError('Passwords do not match');
            return;
        }

        if (formData.password.length < 6) {
            setError('Password must be at least 6 characters long');
            return;
        }

        setLoading(true);
        setError('');

        // Remove confirmPassword from data sent to server
        const { confirmPassword, ...registrationData } = formData;

        const result = await register(registrationData);

        if (result.success) {
            // Show success message and redirect to login
            setError('');
            // Use a more elegant success message instead of alert
            setError(''); // Clear any previous errors
            // Navigate to login page using React Router
            navigate('/login', {
                state: {
                    message: result.message || 'Account created successfully! Please log in.',
                    type: 'success'
                }
            });
        } else {
            setError(result.error);
        }

        setLoading(false);
    };

    return (
        <div className="auth-container">
            {/* Animated Chat Background */}
            <AnimatedChatBackground />

            {/* Telegram-style header bar */}
            <div className="auth-header-bar">
                <div className="auth-logo">
                    <div className="auth-logo-icon">✈️</div>
                    <span>Telegram</span>
                </div>
                <div className="auth-header-spacer"></div>
            </div>

            {/* Main content */}
            <div className="auth-content">
                <div className="auth-page-header">
                    <div className="auth-page-icon">✈️</div>
                    <h1 className="auth-page-title">Sign up for Telegram</h1>
                    <p className="auth-page-subtitle">Please enter your details to create your account.</p>
                </div>

                <div className="auth-card">

                    <form onSubmit={handleSubmit} className="auth-form">
                        {error && (
                            <div className="error-message">
                                {error}
                            </div>
                        )}

                        <div className="form-row">
                            <div className="form-group">
                                <input
                                    type="text"
                                    id="first_name"
                                    name="first_name"
                                    value={formData.first_name}
                                    onChange={handleChange}
                                    placeholder="First name"
                                    disabled={loading}
                                />
                            </div>

                            <div className="form-group">
                                <input
                                    type="text"
                                    id="last_name"
                                    name="last_name"
                                    value={formData.last_name}
                                    onChange={handleChange}
                                    placeholder="Last name"
                                    disabled={loading}
                                />
                            </div>

                            <div className="form-group">
                                <input
                                    type="text"
                                    id="username"
                                    name="username"
                                    value={formData.username}
                                    onChange={handleChange}
                                    placeholder="Username"
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                    placeholder="Email address"
                                    disabled={loading}
                                />
                            </div>

                            <div className="form-group">
                                <input
                                    type="tel"
                                    id="phone"
                                    name="phone"
                                    value={formData.phone}
                                    onChange={handleChange}
                                    placeholder="Phone number"
                                    disabled={loading}
                                />
                            </div>

                            <div className="form-group">
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleChange}
                                    placeholder="Password"
                                    disabled={loading}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <input
                                    type="password"
                                    id="confirmPassword"
                                    name="confirmPassword"
                                    value={formData.confirmPassword}
                                    onChange={handleChange}
                                    placeholder="Confirm password"
                                    disabled={loading}
                                    required
                                />
                            </div>
                        </div>

                        <button
                            type="submit"
                            className={`auth-button ${loading ? 'loading' : ''}`}
                            disabled={loading}
                        >
                            <span>{loading ? 'Creating Account...' : 'Sign Up'}</span>
                        </button>
                    </form>
                </div>

                <div className="auth-footer">
                    <p>
                        Already have an account?{' '}
                        <Link to="/login" className="auth-link">
                            Sign in to Telegram
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default Register;
