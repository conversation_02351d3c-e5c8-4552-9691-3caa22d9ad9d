const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const conversationRoutes = require('./routes/conversations');
const messageRoutes = require('./routes/messages');
const { router: sessionRoutes } = require('./server/routes/sessions');

// Import socket handler
const SocketHandler = require('./socket/socketHandler');

// Import database
const db = require('./database/db');

const app = express();
const server = http.createServer(app);

// Configure CORS for Socket.IO
const io = socketIo(server, {
    cors: {
        origin: function (origin, callback) {
            // Allow requests with no origin (like mobile apps or curl requests)
            if (!origin) return callback(null, true);

            // Allow localhost and any IP address on port 5173 for development
            if (process.env.NODE_ENV === 'development') {
                if (origin.includes(':5173') || origin.includes('localhost')) {
                    return callback(null, true);
                }
            }

            // Allow specific CLIENT_URL if set
            if (process.env.CLIENT_URL && origin === process.env.CLIENT_URL) {
                return callback(null, true);
            }

            // Reject other origins
            callback(new Error('Not allowed by CORS'));
        },
        methods: ["GET", "POST"],
        credentials: true
    }
});

// Middleware
app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        // Allow localhost and any IP address on port 5173 for development
        if (process.env.NODE_ENV === 'development') {
            if (origin.includes(':5173') || origin.includes('localhost')) {
                return callback(null, true);
            }
        }

        // Allow specific CLIENT_URL if set
        if (process.env.CLIENT_URL && origin === process.env.CLIENT_URL) {
            return callback(null, true);
        }

        // Reject other origins
        callback(new Error('Not allowed by CORS'));
    },
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Trust proxy for getting real IP addresses
app.set('trust proxy', true);

// Static files (for file uploads later)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/conversations', conversationRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/sessions', sessionRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Basic route
app.get('/', (req, res) => {
    res.json({ 
        message: 'Telegram Clone API Server',
        version: '1.0.0',
        endpoints: {
            auth: '/api/auth',
            conversations: '/api/conversations',
            messages: '/api/messages',
            health: '/api/health'
        }
    });
});

// Initialize Socket.IO handler
const socketHandler = new SocketHandler(io);

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({ 
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// Handle 404
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
        db.close();
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
        db.close();
        process.exit(0);
    });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📡 Socket.IO server ready`);
    console.log(`🗄️  Database connected`);

    if (process.env.NODE_ENV === 'development') {
        console.log(`🌐 CORS enabled for development (localhost and LAN access)`);

        // Get local IP address for network access
        const os = require('os');
        const networkInterfaces = os.networkInterfaces();
        let localIP = 'localhost';

        // Find the first non-internal IPv4 address
        for (const interfaceName in networkInterfaces) {
            const interfaces = networkInterfaces[interfaceName];
            for (const iface of interfaces) {
                if (iface.family === 'IPv4' && !iface.internal) {
                    localIP = iface.address;
                    break;
                }
            }
            if (localIP !== 'localhost') break;
        }

        console.log(`\n🔗 Server accessible at:`);
        console.log(`   Local:   http://localhost:${PORT}`);
        console.log(`   Network: http://${localIP}:${PORT}`);

        console.log(`\n📋 Available endpoints:`);
        console.log(`   GET  /                     - API info`);
        console.log(`   GET  /api/health           - Health check`);
        console.log(`   POST /api/auth/register    - Register user`);
        console.log(`   POST /api/auth/login       - Login user`);
        console.log(`   POST /api/auth/logout      - Logout user`);
        console.log(`   GET  /api/auth/profile     - Get user profile`);
        console.log(`   GET  /api/conversations    - Get conversations`);
        console.log(`   POST /api/conversations    - Create conversation`);
        console.log(`   GET  /api/messages/conversation/:id - Get messages`);
        console.log(`   POST /api/messages         - Send message`);
    } else {
        console.log(`🌐 CORS enabled for: ${process.env.CLIENT_URL || "production mode"}`);
    }
});

module.exports = { app, server, io };
