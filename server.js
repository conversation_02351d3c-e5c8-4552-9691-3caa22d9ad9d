const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const conversationRoutes = require('./routes/conversations');
const messageRoutes = require('./routes/messages');
const { router: sessionRoutes } = require('./server/routes/sessions');

// Import socket handler
const SocketHandler = require('./socket/socketHandler');

// Import database
const db = require('./database/db');

const app = express();
const server = http.createServer(app);

// Configure CORS for Socket.IO
const io = socketIo(server, {
    cors: {
        origin: process.env.CLIENT_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
    }
});

// Middleware
app.use(cors({
    origin: process.env.CLIENT_URL || "http://localhost:5173",
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Trust proxy for getting real IP addresses
app.set('trust proxy', true);

// Static files (for file uploads later)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/conversations', conversationRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/sessions', sessionRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Basic route
app.get('/', (req, res) => {
    res.json({ 
        message: 'Telegram Clone API Server',
        version: '1.0.0',
        endpoints: {
            auth: '/api/auth',
            conversations: '/api/conversations',
            messages: '/api/messages',
            health: '/api/health'
        }
    });
});

// Initialize Socket.IO handler
const socketHandler = new SocketHandler(io);

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({ 
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// Handle 404
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
        db.close();
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('HTTP server closed');
        db.close();
        process.exit(0);
    });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📡 Socket.IO server ready`);
    console.log(`🗄️  Database connected`);
    console.log(`🌐 CORS enabled for: ${process.env.CLIENT_URL || "http://localhost:5173"}`);
    
    if (process.env.NODE_ENV === 'development') {
        console.log(`\n📋 Available endpoints:`);
        console.log(`   GET  /                     - API info`);
        console.log(`   GET  /api/health           - Health check`);
        console.log(`   POST /api/auth/register    - Register user`);
        console.log(`   POST /api/auth/login       - Login user`);
        console.log(`   POST /api/auth/logout      - Logout user`);
        console.log(`   GET  /api/auth/profile     - Get user profile`);
        console.log(`   GET  /api/conversations    - Get conversations`);
        console.log(`   POST /api/conversations    - Create conversation`);
        console.log(`   GET  /api/messages/conversation/:id - Get messages`);
        console.log(`   POST /api/messages         - Send message`);
    }
});

module.exports = { app, server, io };
