import React, { useState, useEffect } from 'react';
import QRCode from 'react-qr-code';
import { useAuth } from '../../contexts/AuthContext';
import './QRCodeLogin.css';

const QRCodeLogin = ({ onSwitchToForm }) => {
    const [qrData, setQrData] = useState('');
    const [qrStatus, setQrStatus] = useState('generating'); // generating, waiting, expired, success
    const [sessionId, setSessionId] = useState('');
    const { login } = useAuth();

    // Generate QR code data
    useEffect(() => {
        generateQRCode();
    }, []);

    // Poll for QR code authentication status
    useEffect(() => {
        let pollInterval;

        if (sessionId && qrStatus === 'waiting') {
            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/sessions/qr-status/${sessionId}`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.status === 'authenticated') {
                        setQrStatus('success');
                        // Auto-login with the authenticated session
                        const loginResult = await login({ sessionId, qrAuth: true });
                        if (!loginResult.success) {
                            setQrStatus('expired');
                        }
                    } else if (data.status === 'expired') {
                        setQrStatus('expired');
                    }
                } catch (error) {
                    console.error('QR polling error:', error);
                    setQrStatus('expired');
                }
            }, 2000); // Poll every 2 seconds
        }

        return () => {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
        };
    }, [sessionId, qrStatus, login]);

    // Auto-refresh expired QR codes
    useEffect(() => {
        let refreshTimer;

        if (qrStatus === 'expired') {
            refreshTimer = setTimeout(() => {
                generateQRCode();
            }, 3000); // Auto-refresh after 3 seconds
        }

        return () => {
            if (refreshTimer) {
                clearTimeout(refreshTimer);
            }
        };
    }, [qrStatus]);

    const generateQRCode = async () => {
        setQrStatus('generating');
        setQrData(''); // Clear previous QR data

        try {
            const response = await fetch('/api/sessions/generate-qr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                // Add timeout to prevent hanging requests
                signal: AbortSignal.timeout(10000), // 10 second timeout
            });

            // Check if response is ok and has content
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Response is not JSON');
            }

            const data = await response.json();

            if (data.success) {
                setSessionId(data.sessionId);

                // Decode the base64 QR data to get the original JSON string
                try {
                    const decodedQrData = atob(data.qrData);
                    setQrData(decodedQrData);
                } catch (decodeError) {
                    console.error('Failed to decode QR data:', decodeError);
                    // Fallback to using the data as-is
                    setQrData(data.qrData);
                }

                setQrStatus('waiting');

                // Set expiration timer (5 minutes) - use a ref to track current status
                setTimeout(() => {
                    setQrStatus(currentStatus => {
                        // Only expire if still waiting
                        return currentStatus === 'waiting' ? 'expired' : currentStatus;
                    });
                }, 300000); // 5 minutes
            } else {
                console.error('QR generation failed:', data.error);
                setQrStatus('expired');
            }
        } catch (error) {
            console.error('QR generation error:', error);

            // Provide more specific error handling
            if (error.message.includes('404')) {
                console.error('QR generation endpoint not found. Check server configuration.');
            } else if (error.message.includes('Failed to fetch')) {
                console.error('Network error: Unable to connect to server.');
            } else if (error.message.includes('HTTP error')) {
                console.error('Server error:', error.message);
            }

            setQrStatus('expired');
        }
    };

    const handleRefresh = () => {
        generateQRCode();
    };

    const getStatusMessage = () => {
        switch (qrStatus) {
            case 'generating':
                return 'Generating QR code...';
            case 'waiting':
                return 'Scan this QR code with your mobile device';
            case 'expired':
                return 'QR code expired or failed to generate. Click refresh to try again.';
            case 'success':
                return 'Authentication successful!';
            default:
                return '';
        }
    };

    const getStatusIcon = () => {
        switch (qrStatus) {
            case 'generating':
                return '⏳';
            case 'waiting':
                return '📱';
            case 'expired':
                return '🔄';
            case 'success':
                return '✅';
            default:
                return '📱';
        }
    };

    return (
        <div className="qr-login-container">
            <div className="qr-code-section">
                <div className={`qr-code-wrapper ${qrStatus}`}>
                    {qrStatus === 'generating' ? (
                        <div className="qr-loading">
                            <div className="qr-spinner"></div>
                        </div>
                    ) : qrStatus === 'success' ? (
                        <div className="qr-success">
                            <div className="success-icon">✅</div>
                            <div className="success-text">Authenticated!</div>
                        </div>
                    ) : (
                        <div className="qr-code-display">
                            {qrData ? (
                                <QRCode
                                    value={qrData}
                                    size={200}
                                    bgColor="#ffffff"
                                    fgColor="#000000"
                                    level="M"
                                />
                            ) : (
                                <div className="qr-loading">
                                    <div className="qr-spinner"></div>
                                </div>
                            )}
                            {qrStatus === 'expired' && (
                                <div className="qr-overlay">
                                    <div className="expired-message">
                                        <div className="expired-icon">⏰</div>
                                        <div className="expired-text">Expired</div>
                                        <button
                                            className="refresh-btn"
                                            onClick={handleRefresh}
                                        >
                                            Refresh
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                <div className="qr-status">
                    <div className="status-icon">{getStatusIcon()}</div>
                    <div className="status-message">{getStatusMessage()}</div>
                </div>
            </div>

            <div className="qr-instructions">
                <h3>How to scan:</h3>
                <ol>
                    <li>Open Telegram on your mobile device</li>
                    <li>Go to Settings → Devices → Link Desktop Device</li>
                    <li>Point your camera at this QR code</li>
                    <li>Confirm the login on your mobile device</li>
                </ol>
            </div>

            <div className="qr-alternative">
                <button
                    className="switch-to-form-btn"
                    onClick={onSwitchToForm}
                >
                    Use phone number or email instead
                </button>
            </div>
        </div>
    );
};

export default QRCodeLogin;
