/* Session Manager Styles */
.session-manager {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--tg-spacing-xxl);
    background: var(--tg-bg-color);
    color: var(--tg-text-color);
    font-family: var(--tg-font-family);
}

.session-header {
    text-align: center;
    margin-bottom: var(--tg-spacing-xxl);
}

.session-header h2 {
    font-size: var(--tg-font-size-xlarge);
    font-weight: var(--tg-font-weight-medium);
    color: var(--tg-text-color);
    margin: 0 0 var(--tg-spacing-md) 0;
}

.session-header p {
    font-size: var(--tg-font-size-normal);
    color: var(--tg-text-secondary);
    margin: 0;
}

.session-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--tg-spacing-xxl);
    gap: var(--tg-spacing-lg);
}

.session-loading p {
    color: var(--tg-text-secondary);
    font-size: var(--tg-font-size-normal);
}

.session-error {
    display: flex;
    align-items: center;
    gap: var(--tg-spacing-md);
    padding: var(--tg-spacing-lg) var(--tg-spacing-xl);
    background: rgba(229, 62, 62, 0.1);
    border: 1px solid rgba(229, 62, 62, 0.3);
    border-radius: var(--tg-radius-medium);
    color: var(--tg-destructive);
    margin-bottom: var(--tg-spacing-xl);
}

.error-icon {
    font-size: 18px;
}

.error-close {
    background: none;
    border: none;
    color: var(--tg-destructive);
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background var(--tg-transition-normal);
}

.error-close:hover {
    background: rgba(229, 62, 62, 0.2);
}

.session-actions {
    margin-bottom: var(--tg-spacing-xl);
    text-align: center;
}

.terminate-all-btn {
    background: var(--tg-destructive);
    color: white;
    border: none;
    padding: var(--tg-spacing-md) var(--tg-spacing-xl);
    border-radius: var(--tg-radius-medium);
    font-size: var(--tg-font-size-normal);
    font-weight: var(--tg-font-weight-medium);
    cursor: pointer;
    transition: all var(--tg-transition-normal);
    font-family: var(--tg-font-family);
}

.terminate-all-btn:hover:not(:disabled) {
    background: #d32f2f;
    transform: translateY(-1px);
}

.terminate-all-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.session-list {
    display: flex;
    flex-direction: column;
    gap: var(--tg-spacing-lg);
}

.session-item {
    display: flex;
    align-items: flex-start;
    gap: var(--tg-spacing-lg);
    padding: var(--tg-spacing-xl);
    background: var(--tg-card-bg);
    border: 1px solid var(--tg-border-color);
    border-radius: var(--tg-radius-large);
    transition: all var(--tg-transition-normal);
}

.session-item:hover {
    border-color: rgba(64, 167, 227, 0.3);
    box-shadow: var(--tg-shadow-light);
}

.session-item.current {
    border-color: var(--tg-button-color);
    background: rgba(64, 167, 227, 0.05);
}

.session-icon {
    font-size: 32px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--tg-secondary-bg);
    border-radius: var(--tg-radius-medium);
    flex-shrink: 0;
}

.session-info {
    flex: 1;
    min-width: 0;
}

.session-device {
    display: flex;
    align-items: center;
    gap: var(--tg-spacing-md);
    margin-bottom: var(--tg-spacing-md);
}

.device-name {
    font-size: var(--tg-font-size-medium);
    font-weight: var(--tg-font-weight-medium);
    color: var(--tg-text-color);
}

.current-badge {
    background: var(--tg-button-color);
    color: white;
    padding: 2px var(--tg-spacing-sm);
    border-radius: var(--tg-radius-small);
    font-size: var(--tg-font-size-small);
    font-weight: var(--tg-font-weight-medium);
}

.session-details {
    display: flex;
    flex-direction: column;
    gap: var(--tg-spacing-xs);
}

.session-detail {
    display: flex;
    align-items: center;
    gap: var(--tg-spacing-sm);
    font-size: var(--tg-font-size-small);
    color: var(--tg-text-secondary);
}

.detail-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.session-actions {
    display: flex;
    align-items: center;
    gap: var(--tg-spacing-sm);
}

.terminate-btn {
    background: var(--tg-destructive);
    color: white;
    border: none;
    padding: var(--tg-spacing-sm);
    border-radius: var(--tg-radius-small);
    font-size: 16px;
    cursor: pointer;
    transition: all var(--tg-transition-normal);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.terminate-btn:hover:not(:disabled) {
    background: #d32f2f;
    transform: scale(1.1);
}

.terminate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.no-sessions {
    text-align: center;
    padding: var(--tg-spacing-xxl);
    color: var(--tg-text-secondary);
}

.no-sessions-icon {
    font-size: 64px;
    margin-bottom: var(--tg-spacing-lg);
}

.no-sessions h3 {
    font-size: var(--tg-font-size-large);
    font-weight: var(--tg-font-weight-medium);
    color: var(--tg-text-color);
    margin: 0 0 var(--tg-spacing-md) 0;
}

.no-sessions p {
    font-size: var(--tg-font-size-normal);
    margin: 0;
}

.session-info-note {
    display: flex;
    gap: var(--tg-spacing-md);
    padding: var(--tg-spacing-lg);
    background: rgba(64, 167, 227, 0.1);
    border: 1px solid rgba(64, 167, 227, 0.2);
    border-radius: var(--tg-radius-medium);
    margin-top: var(--tg-spacing-xxl);
}

.info-icon {
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 2px;
}

.info-text {
    font-size: var(--tg-font-size-small);
    color: var(--tg-text-secondary);
    line-height: 1.5;
}

.info-text p {
    margin: 0 0 var(--tg-spacing-sm) 0;
}

.info-text p:last-child {
    margin-bottom: 0;
}

.info-text strong {
    color: var(--tg-text-color);
    font-weight: var(--tg-font-weight-medium);
}

/* Responsive Design */
@media (max-width: 768px) {
    .session-manager {
        padding: var(--tg-spacing-lg);
    }
    
    .session-item {
        padding: var(--tg-spacing-lg);
        gap: var(--tg-spacing-md);
    }
    
    .session-icon {
        font-size: 24px;
        width: 40px;
        height: 40px;
    }
    
    .session-device {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--tg-spacing-xs);
    }
    
    .session-details {
        gap: var(--tg-spacing-xs);
    }
    
    .session-detail {
        font-size: var(--tg-font-size-small);
    }
}

@media (max-width: 480px) {
    .session-manager {
        padding: var(--tg-spacing-md);
    }
    
    .session-item {
        flex-direction: column;
        text-align: center;
    }
    
    .session-actions {
        align-self: center;
    }
    
    .terminate-all-btn {
        width: 100%;
        padding: var(--tg-spacing-lg);
    }
}
