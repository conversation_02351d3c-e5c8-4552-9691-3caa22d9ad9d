import axios from 'axios';

// Use relative URL for API calls to work with both localhost and network access
// This will automatically use the same host as the frontend
const API_BASE_URL = import.meta.env.VITE_API_URL || `${window.location.protocol}//${window.location.hostname}:3000/api`;

// Create axios instance
const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add token to requests
api.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Handle token expiration
api.interceptors.response.use(
    (response) => response,
    (error) => {
        // Only redirect on 401 for authenticated routes, not login/register
        if (error.response?.status === 401) {
            const requestUrl = error.config?.url || '';

            // Don't redirect if this is a login or register request
            if (!requestUrl.includes('/auth/login') && !requestUrl.includes('/auth/register')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');

                // Only redirect if we're not already on the login page
                if (window.location.pathname !== '/login') {
                    window.location.href = '/login';
                }
            }
        }
        return Promise.reject(error);
    }
);

// Auth API
export const authAPI = {
    register: (userData) => api.post('/auth/register', userData),
    login: (credentials) => api.post('/auth/login', credentials),
    logout: () => api.post('/auth/logout'),
    getProfile: () => api.get('/auth/profile'),
    verifyToken: () => api.get('/auth/verify'),
};

// Conversations API
export const conversationsAPI = {
    getAll: () => api.get('/conversations'),
    create: (conversationData) => api.post('/conversations', conversationData),
    getById: (id) => api.get(`/conversations/${id}`),
    addMember: (id, userData) => api.post(`/conversations/${id}/members`, userData),
    createDirect: (userId) => api.post('/conversations/direct', { user_id: userId }),
};

// Messages API
export const messagesAPI = {
    getConversationMessages: (conversationId, limit = 50, offset = 0) => 
        api.get(`/messages/conversation/${conversationId}?limit=${limit}&offset=${offset}`),
    send: (messageData) => api.post('/messages', messageData),
    getById: (id) => api.get(`/messages/${id}`),
    edit: (id, content) => api.put(`/messages/${id}`, { content }),
    delete: (id) => api.delete(`/messages/${id}`),
    react: (id, emoji) => api.post(`/messages/${id}/reactions`, { emoji }),
    markAsRead: (conversationId, messageId) => 
        api.post('/messages/read', { conversation_id: conversationId, message_id: messageId }),
};

export default api;
