{"name": "telegram-clone", "version": "1.0.0", "description": "A Telegram-like chat application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "cd client && npm run dev", "dev:all": "concurrently \"npm run dev\" \"npm run client\""}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "geoip-lite": "^1.4.10", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "sqlite3": "^5.1.6", "ua-parser-js": "^2.0.3", "uuid": "^9.0.1"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.2"}, "keywords": ["chat", "telegram", "socket.io", "react"], "author": "Your Name", "license": "MIT"}