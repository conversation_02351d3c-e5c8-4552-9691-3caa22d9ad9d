const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const UAParser = require('ua-parser-js');
const geoip = require('geoip-lite');
const auth = require('../middleware/auth');

// In-memory session store (in production, use Redis or database)
const activeSessions = new Map();
const qrSessions = new Map();

// Helper function to get device info
const getDeviceInfo = (req) => {
    const parser = new UAParser(req.headers['user-agent']);
    const result = parser.getResult();
    const ip = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    const geo = geoip.lookup(ip);
    
    return {
        browser: `${result.browser.name} ${result.browser.version}`,
        os: `${result.os.name} ${result.os.version}`,
        device: result.device.type || 'desktop',
        ip: ip,
        location: geo ? `${geo.city}, ${geo.country}` : 'Unknown',
        userAgent: req.headers['user-agent']
    };
};

// Generate QR code for login
router.post('/generate-qr', async (req, res) => {
    try {
        const sessionId = crypto.randomUUID();
        const secret = crypto.randomBytes(32).toString('hex');
        const timestamp = Date.now();
        
        // Create QR data with session info
        const qrData = JSON.stringify({
            sessionId,
            secret,
            timestamp,
            action: 'telegram_login',
            version: '1.0'
        });
        
        // Store QR session (expires in 5 minutes)
        qrSessions.set(sessionId, {
            secret,
            timestamp,
            status: 'waiting',
            deviceInfo: getDeviceInfo(req),
            expiresAt: timestamp + (5 * 60 * 1000) // 5 minutes
        });
        
        // Auto-cleanup expired QR sessions
        setTimeout(() => {
            if (qrSessions.has(sessionId)) {
                qrSessions.delete(sessionId);
            }
        }, 5 * 60 * 1000);
        
        res.json({
            success: true,
            sessionId,
            qrData: Buffer.from(qrData).toString('base64'),
            expiresIn: 300 // 5 minutes
        });
    } catch (error) {
        console.error('QR generation error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate QR code'
        });
    }
});

// Check QR code authentication status
router.get('/qr-status/:sessionId', (req, res) => {
    const { sessionId } = req.params;
    const qrSession = qrSessions.get(sessionId);
    
    if (!qrSession) {
        return res.json({ status: 'expired' });
    }
    
    // Check if expired
    if (Date.now() > qrSession.expiresAt) {
        qrSessions.delete(sessionId);
        return res.json({ status: 'expired' });
    }
    
    res.json({ 
        status: qrSession.status,
        timestamp: qrSession.timestamp
    });
});

// Authenticate QR code (called from mobile app)
router.post('/authenticate-qr', auth, async (req, res) => {
    try {
        const { sessionId, secret } = req.body;
        const qrSession = qrSessions.get(sessionId);
        
        if (!qrSession || qrSession.secret !== secret) {
            return res.status(400).json({
                success: false,
                error: 'Invalid or expired QR session'
            });
        }
        
        // Check if expired
        if (Date.now() > qrSession.expiresAt) {
            qrSessions.delete(sessionId);
            return res.status(400).json({
                success: false,
                error: 'QR code expired'
            });
        }
        
        // Create new session for the QR login
        const newSessionId = crypto.randomUUID();
        const token = jwt.sign(
            { userId: req.user.id, sessionId: newSessionId },
            process.env.JWT_SECRET,
            { expiresIn: '30d' }
        );
        
        // Store the new session
        activeSessions.set(newSessionId, {
            userId: req.user.id,
            token,
            deviceInfo: qrSession.deviceInfo,
            createdAt: Date.now(),
            lastActive: Date.now(),
            loginMethod: 'qr_code'
        });
        
        // Mark QR session as authenticated
        qrSession.status = 'authenticated';
        qrSession.token = token;
        qrSession.sessionId = newSessionId;
        
        res.json({
            success: true,
            message: 'QR authentication successful'
        });
    } catch (error) {
        console.error('QR authentication error:', error);
        res.status(500).json({
            success: false,
            error: 'Authentication failed'
        });
    }
});

// Get user's active sessions
router.get('/active', auth, (req, res) => {
    try {
        const userSessions = [];
        
        for (const [sessionId, session] of activeSessions.entries()) {
            if (session.userId === req.user.id) {
                userSessions.push({
                    sessionId,
                    deviceInfo: session.deviceInfo,
                    createdAt: session.createdAt,
                    lastActive: session.lastActive,
                    loginMethod: session.loginMethod || 'password',
                    isCurrent: sessionId === req.sessionId
                });
            }
        }
        
        // Sort by last active (most recent first)
        userSessions.sort((a, b) => b.lastActive - a.lastActive);
        
        res.json({
            success: true,
            sessions: userSessions
        });
    } catch (error) {
        console.error('Get sessions error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve sessions'
        });
    }
});

// Terminate a specific session
router.delete('/:sessionId', auth, (req, res) => {
    try {
        const { sessionId } = req.params;
        const session = activeSessions.get(sessionId);
        
        if (!session) {
            return res.status(404).json({
                success: false,
                error: 'Session not found'
            });
        }
        
        // Check if user owns this session
        if (session.userId !== req.user.id) {
            return res.status(403).json({
                success: false,
                error: 'Unauthorized'
            });
        }
        
        // Remove session
        activeSessions.delete(sessionId);
        
        res.json({
            success: true,
            message: 'Session terminated successfully'
        });
    } catch (error) {
        console.error('Terminate session error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to terminate session'
        });
    }
});

// Terminate all other sessions (keep current)
router.delete('/terminate/others', auth, (req, res) => {
    try {
        let terminatedCount = 0;
        
        for (const [sessionId, session] of activeSessions.entries()) {
            if (session.userId === req.user.id && sessionId !== req.sessionId) {
                activeSessions.delete(sessionId);
                terminatedCount++;
            }
        }
        
        res.json({
            success: true,
            message: `Terminated ${terminatedCount} sessions`,
            terminatedCount
        });
    } catch (error) {
        console.error('Terminate other sessions error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to terminate sessions'
        });
    }
});

// Update session activity (called on API requests)
const updateSessionActivity = (sessionId) => {
    const session = activeSessions.get(sessionId);
    if (session) {
        session.lastActive = Date.now();
    }
};

// Export session management functions
module.exports = {
    router,
    activeSessions,
    qrSessions,
    updateSessionActivity,
    getDeviceInfo
};
