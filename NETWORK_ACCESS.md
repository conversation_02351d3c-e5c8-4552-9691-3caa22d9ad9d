# Network Access Configuration

This document explains how to access the Telegram chat application from other devices on your local area network (LAN).

## Quick Start

### 1. Start the Backend Server
```bash
# From the project root directory
NODE_ENV=development node server.js
```

### 2. Start the Frontend Server
```bash
# From the client directory
cd client
npm run dev
```

### 3. Access from Other Devices
The servers will display network URLs when they start:

**Backend Server:**
```
🔗 Server accessible at:
   Local:   http://localhost:3000
   Network: http://[YOUR_IP]:3000
```

**Frontend Server:**
```
➜  Local:   http://localhost:5173/
➜  Network: http://[YOUR_IP]:5173/
```

## Network URLs

Replace `[YOUR_IP]` with your actual local IP address (e.g., *************):

- **Frontend Application:** `http://[YOUR_IP]:5173`
- **Backend API:** `http://[YOUR_IP]:3000`

## Testing from Other Devices

### Mobile Devices
1. Connect your mobile device to the same WiFi network
2. Open a web browser on your mobile device
3. Navigate to `http://[YOUR_IP]:5173`
4. The application should load and function normally

### Other Computers
1. Ensure the other computer is on the same network
2. Open any web browser
3. Navigate to `http://[YOUR_IP]:5173`
4. All features should work including real-time messaging

## Features That Work Over LAN

✅ **Authentication**
- Login with username/password
- Registration of new accounts
- QR code login (when mobile app is available)

✅ **Real-time Messaging**
- Send and receive messages instantly
- Socket.IO connections work over LAN
- Message history and conversations

✅ **All UI Features**
- Responsive design adapts to different devices
- Dark/light theme switching
- Mobile-optimized interface

## Troubleshooting

### Cannot Access from Other Devices

1. **Check Firewall Settings**
   - Ensure ports 3000 and 5173 are not blocked
   - Temporarily disable firewall to test

2. **Verify Network Connection**
   - Ensure all devices are on the same network
   - Check if you can ping the host machine

3. **Check IP Address**
   - The IP address may change if using DHCP
   - Restart servers to get updated IP addresses

### CORS Errors

The application is configured to allow CORS from any IP address on port 5173 in development mode. If you encounter CORS errors:

1. Ensure `NODE_ENV=development` is set
2. Check that the origin includes `:5173`
3. Restart the backend server

### Proxy Issues

If API calls fail:

1. Check the Vite proxy configuration in `client/vite.config.js`
2. Verify the backend server is accessible directly
3. Check the browser's Network tab for failed requests

## Production Deployment

For production deployment over a network:

1. Set appropriate environment variables:
   ```bash
   CLIENT_URL=http://[YOUR_DOMAIN]:5173
   NODE_ENV=production
   ```

2. Configure proper CORS origins in `server.js`

3. Use a reverse proxy (nginx) for better security

## Security Considerations

⚠️ **Development Mode Only**
- This configuration is intended for development and testing
- Do not expose these ports to the internet
- Use proper authentication and HTTPS in production

⚠️ **Local Network Only**
- Only devices on the same local network can access the application
- Ensure your network is secure and trusted

## Environment Variables

Create a `.env` file in the project root:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Client Configuration (optional)
CLIENT_URL=http://[YOUR_IP]:5173

# Database
DB_PATH=./database/telegram.db

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key
```

Create a `.env` file in the `client` directory:

```env
# API Configuration (optional - auto-detected if not set)
VITE_API_URL=http://[YOUR_IP]:3000/api
VITE_SOCKET_URL=http://[YOUR_IP]:3000
```

## Technical Details

### Auto-Detection
The application automatically detects the correct IP address for:
- Socket.IO connections
- API calls
- CORS configuration

### Proxy Configuration
Vite is configured to proxy `/api/*` requests to the backend server, ensuring seamless communication between frontend and backend.

### CORS Configuration
The backend allows requests from any IP address on port 5173 in development mode, enabling LAN access while maintaining security.
