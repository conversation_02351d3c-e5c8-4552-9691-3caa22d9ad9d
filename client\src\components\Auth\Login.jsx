import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import AnimatedChat<PERSON>ackground from './AnimatedChatBackground';
import QRCodeLogin from './QRCodeLogin';
import './Auth.css';

const Login = () => {
    const [formData, setFormData] = useState({
        username: '',
        password: ''
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [showQRCode, setShowQRCode] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const { login } = useAuth();
    const { setAuthMode } = useTheme();
    const location = useLocation();

    // Set auth mode when component mounts
    useEffect(() => {
        setAuthMode(true);
        return () => setAuthMode(false); // Cleanup when component unmounts
    }, [setAuthMode]);

    // Handle success message from registration
    useEffect(() => {
        if (location.state?.message && location.state?.type === 'success') {
            setSuccessMessage(location.state.message);
            // Clear the message after 5 seconds
            setTimeout(() => setSuccessMessage(''), 5000);
        }
    }, [location.state]);

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
        // Clear error and success messages when user starts typing
        if (error) setError('');
        if (successMessage) setSuccessMessage('');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.username || !formData.password) {
            setError('Please fill in all fields');
            return;
        }

        setLoading(true);
        setError('');
        setSuccessMessage(''); // Clear any success messages

        try {
            const result = await login(formData);

            if (!result.success) {
                setError(result.error);
                // Keep form data intact for user convenience
                // Don't clear formData on error
            }
            // On success, the AuthContext will handle navigation
        } catch (error) {
            console.error('Login submission error:', error);
            setError('An unexpected error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="auth-container">
            {/* Animated Chat Background */}
            <AnimatedChatBackground />

            {/* Telegram-style header bar - No theme toggle in auth mode */}
            <div className="auth-header-bar">
                <div className="auth-logo">
                    <div className="auth-logo-icon">✈️</div>
                    <span>Telegram</span>
                </div>
                <div className="auth-header-spacer"></div>
            </div>

            {/* Main content */}
            <div className="auth-content">
                <div className="auth-page-header">
                    <div className="auth-page-icon">✈️</div>
                    <h1 className="auth-page-title">Sign in to Telegram</h1>
                    <p className="auth-page-subtitle">
                        {showQRCode
                            ? "Scan QR code with your mobile device"
                            : "Please confirm your country and enter your phone number."
                        }
                    </p>
                </div>

                <div className="auth-card">
                    {/* Login Method Toggle */}
                    <div className="login-method-toggle">
                        <button
                            type="button"
                            className={`method-btn ${!showQRCode ? 'active' : ''}`}
                            onClick={() => setShowQRCode(false)}
                        >
                            📱 Phone/Email
                        </button>
                        <button
                            type="button"
                            className={`method-btn ${showQRCode ? 'active' : ''}`}
                            onClick={() => setShowQRCode(true)}
                        >
                            📷 QR Code
                        </button>
                    </div>

                    {showQRCode ? (
                        <QRCodeLogin onSwitchToForm={() => setShowQRCode(false)} />
                    ) : (
                        <form onSubmit={handleSubmit} className="auth-form">
                            {successMessage && (
                                <div className="success-message">
                                    {successMessage}
                                </div>
                            )}
                            {error && (
                                <div className="error-message">
                                    {error}
                                </div>
                            )}

                            <div className="form-row">
                                <div className="form-group">
                                    <input
                                        type="text"
                                        id="username"
                                        name="username"
                                        value={formData.username}
                                        onChange={handleChange}
                                        placeholder="Phone number, username or email"
                                        disabled={loading}
                                        autoComplete="username"
                                    />
                                </div>

                                <div className="form-group">
                                    <input
                                        type="password"
                                        id="password"
                                        name="password"
                                        value={formData.password}
                                        onChange={handleChange}
                                        placeholder="Password"
                                        disabled={loading}
                                        autoComplete="current-password"
                                    />
                                </div>
                            </div>

                            <button
                                type="submit"
                                className={`auth-button ${loading ? 'loading' : ''}`}
                                disabled={loading}
                            >
                                <span>{loading ? 'Signing In...' : 'Sign In'}</span>
                            </button>
                        </form>
                    )}
                </div>

                <div className="auth-footer">
                    <p>
                        Don't have an account?{' '}
                        <Link to="/register" className="auth-link">
                            Sign up for Telegram
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default Login;
