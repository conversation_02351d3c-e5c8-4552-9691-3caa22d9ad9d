import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import AnimatedChatBackground from './AnimatedChatBackground';
import QRCodeLogin from './QRCodeLogin';
import './Auth.css';

const Login = () => {
    const [formData, setFormData] = useState({
        username: '',
        password: ''
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [showQRCode, setShowQRCode] = useState(false);
    const { login } = useAuth();
    const { setAuthMode } = useTheme();

    // Set auth mode when component mounts
    useEffect(() => {
        setAuthMode(true);
        return () => setAuthMode(false); // Cleanup when component unmounts
    }, [setAuthMode]);

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
        // Clear error when user starts typing
        if (error) setError('');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.username || !formData.password) {
            setError('Please fill in all fields');
            return;
        }

        setLoading(true);
        setError('');

        const result = await login(formData);

        if (!result.success) {
            setError(result.error);
        }

        setLoading(false);
    };

    return (
        <div className="auth-container">
            {/* Animated Chat Background */}
            <AnimatedChatBackground />

            {/* Telegram-style header bar - No theme toggle in auth mode */}
            <div className="auth-header-bar">
                <div className="auth-logo">
                    <div className="auth-logo-icon">✈️</div>
                    <span>Telegram</span>
                </div>
                <div className="auth-header-spacer"></div>
            </div>

            {/* Main content */}
            <div className="auth-content">
                <div className="auth-page-header">
                    <div className="auth-page-icon">✈️</div>
                    <h1 className="auth-page-title">Sign in to Telegram</h1>
                    <p className="auth-page-subtitle">Please confirm your country and enter your phone number.</p>
                </div>

                <div className="auth-card">

                    <form onSubmit={handleSubmit} className="auth-form">
                        {error && (
                            <div className="error-message">
                                {error}
                            </div>
                        )}

                        <div className="form-row">
                            <div className="form-group">
                                <input
                                    type="text"
                                    id="username"
                                    name="username"
                                    value={formData.username}
                                    onChange={handleChange}
                                    placeholder="Phone number, username or email"
                                    disabled={loading}
                                    autoComplete="username"
                                />
                            </div>

                            <div className="form-group">
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleChange}
                                    placeholder="Password"
                                    disabled={loading}
                                    autoComplete="current-password"
                                />
                            </div>
                        </div>

                        <button
                            type="submit"
                            className={`auth-button ${loading ? 'loading' : ''}`}
                            disabled={loading}
                        >
                            <span>{loading ? 'Signing In...' : 'Sign In'}</span>
                        </button>
                    </form>
                </div>

                <div className="auth-footer">
                    <p>
                        Don't have an account?{' '}
                        <Link to="/register" className="auth-link">
                            Sign up for Telegram
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default Login;
