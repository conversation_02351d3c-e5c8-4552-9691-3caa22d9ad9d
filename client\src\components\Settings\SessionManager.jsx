import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import './SessionManager.css';

const SessionManager = () => {
    const [sessions, setSessions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [terminatingSession, setTerminatingSession] = useState(null);
    const { token } = useAuth();

    useEffect(() => {
        fetchSessions();
    }, []);

    const fetchSessions = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/sessions/active', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            
            if (data.success) {
                setSessions(data.sessions);
            } else {
                setError(data.error || 'Failed to fetch sessions');
            }
        } catch (err) {
            console.error('Fetch sessions error:', err);
            setError('Failed to load sessions');
        } finally {
            setLoading(false);
        }
    };

    const terminateSession = async (sessionId) => {
        try {
            setTerminatingSession(sessionId);
            const response = await fetch(`/api/sessions/${sessionId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            
            if (data.success) {
                // Remove session from list
                setSessions(prev => prev.filter(s => s.sessionId !== sessionId));
            } else {
                setError(data.error || 'Failed to terminate session');
            }
        } catch (err) {
            console.error('Terminate session error:', err);
            setError('Failed to terminate session');
        } finally {
            setTerminatingSession(null);
        }
    };

    const terminateAllOtherSessions = async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/sessions/terminate/others', {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            
            if (data.success) {
                // Refresh sessions list
                await fetchSessions();
            } else {
                setError(data.error || 'Failed to terminate sessions');
            }
        } catch (err) {
            console.error('Terminate all sessions error:', err);
            setError('Failed to terminate sessions');
        } finally {
            setLoading(false);
        }
    };

    const formatDate = (timestamp) => {
        return new Date(timestamp).toLocaleString();
    };

    const getDeviceIcon = (deviceInfo) => {
        const device = deviceInfo.device?.toLowerCase() || '';
        const browser = deviceInfo.browser?.toLowerCase() || '';
        const os = deviceInfo.os?.toLowerCase() || '';

        if (device.includes('mobile') || os.includes('android') || os.includes('ios')) {
            return '📱';
        } else if (browser.includes('chrome')) {
            return '🌐';
        } else if (browser.includes('firefox')) {
            return '🦊';
        } else if (browser.includes('safari')) {
            return '🧭';
        } else if (browser.includes('edge')) {
            return '🔷';
        } else {
            return '💻';
        }
    };

    const getLoginMethodIcon = (method) => {
        switch (method) {
            case 'qr_code':
                return '📷';
            case 'password':
                return '🔑';
            default:
                return '🔐';
        }
    };

    if (loading && sessions.length === 0) {
        return (
            <div className="session-manager">
                <div className="session-loading">
                    <div className="tg-spinner tg-spinner-medium"></div>
                    <p>Loading active sessions...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="session-manager">
            <div className="session-header">
                <h2>Active Sessions</h2>
                <p>Manage your active login sessions across all devices</p>
            </div>

            {error && (
                <div className="session-error">
                    <span className="error-icon">⚠️</span>
                    <span>{error}</span>
                    <button onClick={() => setError('')} className="error-close">×</button>
                </div>
            )}

            <div className="session-actions">
                <button 
                    className="terminate-all-btn"
                    onClick={terminateAllOtherSessions}
                    disabled={loading || sessions.filter(s => !s.isCurrent).length === 0}
                >
                    🚫 Terminate All Other Sessions
                </button>
            </div>

            <div className="session-list">
                {sessions.map((session) => (
                    <div 
                        key={session.sessionId} 
                        className={`session-item ${session.isCurrent ? 'current' : ''}`}
                    >
                        <div className="session-icon">
                            {getDeviceIcon(session.deviceInfo)}
                        </div>
                        
                        <div className="session-info">
                            <div className="session-device">
                                <span className="device-name">
                                    {session.deviceInfo.browser} on {session.deviceInfo.os}
                                </span>
                                {session.isCurrent && (
                                    <span className="current-badge">Current Session</span>
                                )}
                            </div>
                            
                            <div className="session-details">
                                <div className="session-detail">
                                    <span className="detail-icon">📍</span>
                                    <span>{session.deviceInfo.location}</span>
                                </div>
                                <div className="session-detail">
                                    <span className="detail-icon">{getLoginMethodIcon(session.loginMethod)}</span>
                                    <span>
                                        {session.loginMethod === 'qr_code' ? 'QR Code' : 'Password'}
                                    </span>
                                </div>
                                <div className="session-detail">
                                    <span className="detail-icon">🕒</span>
                                    <span>Last active: {formatDate(session.lastActive)}</span>
                                </div>
                                <div className="session-detail">
                                    <span className="detail-icon">📅</span>
                                    <span>Created: {formatDate(session.createdAt)}</span>
                                </div>
                            </div>
                        </div>
                        
                        {!session.isCurrent && (
                            <div className="session-actions">
                                <button
                                    className="terminate-btn"
                                    onClick={() => terminateSession(session.sessionId)}
                                    disabled={terminatingSession === session.sessionId}
                                >
                                    {terminatingSession === session.sessionId ? (
                                        <div className="tg-spinner tg-spinner-small"></div>
                                    ) : (
                                        '🗑️'
                                    )}
                                </button>
                            </div>
                        )}
                    </div>
                ))}
            </div>

            {sessions.length === 0 && !loading && (
                <div className="no-sessions">
                    <div className="no-sessions-icon">📱</div>
                    <h3>No Active Sessions</h3>
                    <p>You don't have any active sessions on other devices.</p>
                </div>
            )}

            <div className="session-info-note">
                <div className="info-icon">ℹ️</div>
                <div className="info-text">
                    <p><strong>Security Note:</strong> If you see any suspicious sessions, terminate them immediately and change your password.</p>
                    <p>Sessions automatically expire after 30 days of inactivity.</p>
                </div>
            </div>
        </div>
    );
};

export default SessionManager;
