import React, { useState, useEffect } from 'react';
import './AnimatedChatBackground.css';

const AnimatedChatBackground = () => {
    const [messages, setMessages] = useState([]);
    const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
    const [isMobile, setIsMobile] = useState(false);

    // Detect mobile screen size
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth <= 768);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);

        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    // Sample chat conversations to showcase app functionality
    const chatScenarios = [
        {
            id: 1,
            messages: [
                { id: 1, text: "Hey! How's your day going? 😊", sender: "Alice", isOutgoing: false, delay: 0 },
                { id: 2, text: "Great! Just finished my presentation 🎉", sender: "You", isOutgoing: true, delay: 2000 },
                { id: 3, text: "That's awesome! How did it go?", sender: "Alice", isOutgoing: false, delay: 4000 },
                { id: 4, text: "Really well! The team loved it 💪", sender: "You", isOutgoing: true, delay: 6000 },
            ]
        },
        {
            id: 2,
            messages: [
                { id: 5, text: "Are we still on for dinner tonight?", sender: "Bob", isOutgoing: false, delay: 8000 },
                { id: 6, text: "Absolutely! 7 PM at the usual place?", sender: "You", isOutgoing: true, delay: 10000 },
                { id: 7, text: "Perfect! See you there 🍽️", sender: "Bob", isOutgoing: false, delay: 12000 },
            ]
        },
        {
            id: 3,
            messages: [
                { id: 8, text: "Check out this amazing sunset! 🌅", sender: "Sarah", isOutgoing: false, delay: 14000 },
                { id: 9, text: "Wow! Where is this?", sender: "You", isOutgoing: true, delay: 16000 },
                { id: 10, text: "From my balcony! The view is incredible", sender: "Sarah", isOutgoing: false, delay: 18000 },
                { id: 11, text: "So jealous! 😍", sender: "You", isOutgoing: true, delay: 20000 },
            ]
        },
        {
            id: 4,
            messages: [
                { id: 12, text: "Quick question about the project", sender: "Mike", isOutgoing: false, delay: 22000 },
                { id: 13, text: "Sure, what's up?", sender: "You", isOutgoing: true, delay: 24000 },
                { id: 14, text: "When's the deadline again?", sender: "Mike", isOutgoing: false, delay: 26000 },
                { id: 15, text: "Next Friday, but we're ahead of schedule 👍", sender: "You", isOutgoing: true, delay: 28000 },
            ]
        },
        {
            id: 5,
            messages: [
                { id: 16, text: "Thanks for helping me move today! 📦", sender: "Emma", isOutgoing: false, delay: 30000 },
                { id: 17, text: "No problem! That's what friends are for 😊", sender: "You", isOutgoing: true, delay: 32000 },
                { id: 18, text: "Pizza's on me next time!", sender: "Emma", isOutgoing: false, delay: 34000 },
                { id: 19, text: "Deal! 🍕", sender: "You", isOutgoing: true, delay: 36000 },
            ]
        }
    ];

    // Flatten all messages with their delays
    const allMessages = chatScenarios.flatMap(scenario => scenario.messages);

    // Reduce messages on mobile for better performance and readability
    const messagesToShow = isMobile ? allMessages.slice(0, 8) : allMessages;

    useEffect(() => {
        const timers = [];

        messagesToShow.forEach((message, index) => {
            const timer = setTimeout(() => {
                setMessages(prev => [...prev, { ...message, id: `${message.id}-${Date.now()}` }]);
            }, message.delay);
            timers.push(timer);
        });

        // Reset animation after all messages are shown
        const resetDuration = isMobile ? 25000 : 40000; // Shorter cycle on mobile
        const resetTimer = setTimeout(() => {
            setMessages([]);
            setCurrentMessageIndex(0);
        }, resetDuration);

        timers.push(resetTimer);

        return () => {
            timers.forEach(timer => clearTimeout(timer));
        };
    }, [currentMessageIndex, isMobile, messagesToShow]);

    // Auto-restart animation
    useEffect(() => {
        const restartDuration = isMobile ? 27000 : 42000; // Faster restart on mobile
        const restartTimer = setTimeout(() => {
            setCurrentMessageIndex(prev => prev + 1);
        }, restartDuration);

        return () => clearTimeout(restartTimer);
    }, [currentMessageIndex, isMobile]);

    return (
        <div className="animated-chat-background">
            <div className="chat-bubbles-container">
                {messages.map((message, index) => (
                    <div
                        key={message.id}
                        className={`chat-bubble ${message.isOutgoing ? 'outgoing' : 'incoming'}`}
                        style={{
                            animationDelay: `${index * 0.1}s`,
                            '--random-x': `${Math.random() * 80 + 10}%`,
                            '--random-y': `${Math.random() * 70 + 15}%`,
                        }}
                    >
                        <div className="bubble-content">
                            <div className="bubble-sender">{message.sender}</div>
                            <div className="bubble-text">{message.text}</div>
                        </div>
                        <div className="bubble-tail"></div>
                    </div>
                ))}
            </div>

            {/* Floating feature highlights - reduced on mobile */}
            {!isMobile && (
                <div className="feature-highlights">
                    <div className="feature-highlight" style={{ '--delay': '0s' }}>
                        <div className="feature-icon">🚀</div>
                        <div className="feature-text">Fast & Secure</div>
                    </div>
                    <div className="feature-highlight" style={{ '--delay': '2s' }}>
                        <div className="feature-icon">🔒</div>
                        <div className="feature-text">End-to-End Encrypted</div>
                    </div>
                    <div className="feature-highlight" style={{ '--delay': '4s' }}>
                        <div className="feature-icon">☁️</div>
                        <div className="feature-text">Cloud Synced</div>
                    </div>
                    <div className="feature-highlight" style={{ '--delay': '6s' }}>
                        <div className="feature-icon">📱</div>
                        <div className="feature-text">Multi-Device</div>
                    </div>
                </div>
            )}

            {/* Typing indicators - hidden on very small screens */}
            {window.innerWidth > 360 && (
                <div className="typing-indicators">
                    <div className="typing-indicator" style={{ '--delay': '1s' }}>
                        <div className="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <div className="typing-text">Someone is typing...</div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AnimatedChatBackground;
