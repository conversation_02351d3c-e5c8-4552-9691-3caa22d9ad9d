{"env": {"node": true, "browser": true}, "rules": {"array-bracket-spacing": [2, "never"], "brace-style": [2, "1tbs"], "camelcase": 2, "computed-property-spacing": [2, "never"], "consistent-this": [1, "self"], "default-case": 2, "func-style": [2, "declaration"], "guard-for-in": 2, "indent": [2, 2], "linebreak-style": [2, "unix"], "max-nested-callbacks": 0, "no-console": 0, "no-else-return": 1, "no-eq-null": 2, "no-floating-decimal": 2, "no-irregular-whitespace": 2, "no-lonely-if": 2, "no-multi-spaces": 1, "no-nested-ternary": 2, "no-process-exit": 0, "no-self-compare": 2, "no-shadow": 1, "no-spaced-func": 2, "no-undef": 2, "no-underscore-dangle": 0, "no-unused-vars": [2, {"vars": "all", "args": "after-used"}], "object-curly-spacing": [2, "never"], "quotes": [1, "single", "avoid-escape"], "radix": 2, "require-jsdoc": 2, "semi": [2, "always"], "semi-spacing": [2, {"before": false, "after": true}], "space-after-keywords": [2, "always"], "space-infix-ops": 2, "space-return-throw-case": 2, "valid-jsdoc": 2, "wrap-iife": [2, "inside"]}}