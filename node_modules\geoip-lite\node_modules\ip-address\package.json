{"name": "ip-address", "description": "A library for parsing IPv4 and IPv6 IP addresses in node and the browser.", "keywords": ["ipv6", "ipv4", "browser", "validation"], "version": "5.9.4", "author": "<PERSON> <<EMAIL>> (https://beaugunderson.com/)", "license": "MIT", "main": "ip-address.js", "scripts": {"test": "mocha -R spec", "docs": "documentation build --github --output docs --format html ./ip-address.js", "prepublishOnly": "browserify ./ip-address-globals.js > ./dist/ip-address-globals.js"}, "engines": {"node": ">= 0.10"}, "repository": {"type": "git", "url": "git://github.com/beaugunderson/ip-address.git"}, "dependencies": {"jsbn": "1.1.0", "lodash": "^4.17.15", "sprintf-js": "1.1.2"}, "devDependencies": {"browserify": "^16.3.0", "chai": "^4.2.0", "codecov.io": "^0.1.6", "documentation": "^12.0.3", "istanbul": "^0.4.5", "mocha": "^6.2.0", "mochify": "^6.3.0"}}